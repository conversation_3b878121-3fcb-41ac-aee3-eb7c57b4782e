{"name": "om-admin", "version": "0.1.0", "private": true, "scripts": {"dev": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "eslint --ext .js,.vue .", "test": "is-ci \"test:coverage\" \"test:watch\"", "test:watch": "jest --watch", "test:debug": "node --inspect-brk ./node_modules/jest/bin/jest.js --runInBand --watch", "test:generate": "jest --json --outputFile=.storybook/.jest-test-results.json", "test:coverage": "yarn test:generate --testPathPattern='/*/.*\\.(test|spec)?\\.(js|ts)$' --coverage", "test:routes": "jest ./src/router/guards.spec.js --watch", "test:generate:watch": "yarn test:generate --watch || true", "storybook:dev": "start-storybook -p 9001 -c .storybook -s public", "storybook:build": "build-storybook --quiet"}, "dependencies": {"@fortawesome/fontawesome-free": "^5", "@iconscout/vue-unicons": "^0.0.3", "@om/common": "workspace:^", "@om/custom-theme-styles": "workspace:^", "@om/editor-ssr-shared": "workspace:^", "@om/integrations": "workspace:^", "@om/payment": "workspace:^", "@om/smart-tags": "workspace:^", "@om/template-properties": "workspace:^", "@om/themekit": "workspace:^", "@om/workflow-sppo": "workspace:^", "@tinymce/tinymce-vue": "^3", "@tiptap/core": "^2.11.7", "@tiptap/extension-bold": "^2.11.7", "@tiptap/extension-bullet-list": "^2.11.9", "@tiptap/extension-color": "^2.11.7", "@tiptap/extension-document": "^2.11.7", "@tiptap/extension-font-family": "^2.11.7", "@tiptap/extension-italic": "^2.11.7", "@tiptap/extension-link": "^2.11.7", "@tiptap/extension-list-item": "^2.11.9", "@tiptap/extension-ordered-list": "^2.11.9", "@tiptap/extension-paragraph": "^2.11.7", "@tiptap/extension-strike": "^2.11.7", "@tiptap/extension-text": "^2.11.7", "@tiptap/extension-text-align": "^2.11.7", "@tiptap/extension-text-style": "^2.11.7", "@tiptap/extension-underline": "^2.11.7", "@tiptap/pm": "^2.11.7", "@tiptap/suggestion": "^2.11.7", "@tiptap/vue-2": "^2.11.7", "@voerro/vue-tagsinput": "^1.11.2", "apollo-cache-inmemory": "^1.2.7", "apollo-client": "^2.3.8", "apollo-link": "^1.2.2", "apollo-link-error": "^1.1.0", "apollo-link-http": "^1.5.4", "axios": "^0.26.0", "babel-plugin-prismjs": "^1.0.2", "bootstrap": "4.3.1", "braintree-web-drop-in": "^1.42.0", "canvas-confetti": "^0.4.2", "chart.js": "^2.7.2", "color-hash": "^1.0.3", "color-thief": "^2.2.5", "driver.js": "^0.9.8", "file-saver": "^1.3.8", "flat": "^5.0.0", "flatpickr": "^4.5.1", "font-awesome": "^4.7.0", "graphql": "^15.0.0", "graphql-tag": "^2.9.2", "gridlex": "^2.7.1", "hotkeys-js": "^3.4.1", "intl-tel-input": "^15.0.1", "js-cookie": "^2.2.0", "jszip": "^3.2.2", "lodash-es": "^4.17.21", "logrocket": "^9.0.2", "lottie-web": "^5.6.10", "moment": "^2.22.2", "moment-timezone": "^0.5.40", "nanoid": "^3.1.23", "numeral": "^2.0.6", "open-iconic": "^1.1.1", "patternomaly": "^1.3.0", "portal-vue": "^2.1.7", "prismjs": "^1.15.0", "query-string": "^6.2.0", "quill": "^1.3.7", "quill-emoji": "^0.2.0", "quill-image-resize": "^3.0.9", "shortcut-buttons-flatpickr": "^0.4.0", "skeleton-loader-vue": "^1.0.10", "slugify": "^1.3.5", "sortablejs": "^1.7.0", "storybook-vue-router": "^1.0.7", "stylis": "^3.5.3", "swiper": "^6.6.1", "tinycolor2": "^1.4.1", "tinymce": "^6.6.0", "v-calendar": "^0.9.7", "v-tooltip": "^2.1.3", "vue": "^2.6.11", "vue-apollo": "^3.0.3", "vue-awesome-swiper": "^4.1.1", "vue-chartjs": "^3.4.0", "vue-clipboard2": "^0.2.1", "vue-cropperjs": "^4.2.0", "vue-frag": "^1.4.2", "vue-gtm": "^2.0.0", "vue-i18n": "^8.14.1", "vue-infinite-loading": "^2.4.4", "vue-inline-svg": "^2.0.0", "vue-input-autowidth": "^1.0.2", "vue-invisible-recaptcha": "^0.2.1", "vue-js-modal": "1.3.15", "vue-js-toggle-button": "^1.2.3", "vue-multiselect": "^2.1.0", "vue-password-strength-meter": "^1.7.1", "vue-popperjs": "^2.3.0", "vue-quill-editor": "^3.0.6", "vue-range-slider": "^0.6.0", "vue-rate": "^2.2.0", "vue-router": "^3.0.1", "vue-scrollto": "^2.11.0", "vue-select": "^2.4.0", "vue-slide-up-down": "^1.6.0", "vue-switches": "^2.0.1", "vue-template-compiler": "2.7.16", "vue2-animate": "^2.1.2", "vuedraggable": "^2.20.0", "vuejs-timepicker": "git+https://github.com/xxRockOnxx/vue2-timepicker.git", "vuelidate": "^0.7.4", "vuex": "^3.0.1", "webfontloader": "^1.6.28", "zxcvbn": "^4.4.2"}, "devDependencies": {"@babel/core": "^7.14.6", "@graphql-tools/jest-transform": "^2.0.0", "@om/jest-num-failed": "workspace:^", "@om/storybook-copy-paste-addon": "workspace:^", "@storybook/addon-essentials": "^6.4.8", "@storybook/addon-jest": "^6.4.8", "@storybook/addon-storysource": "^6.4.8", "@storybook/addons": "^6.4.18", "@storybook/api": "^6.4.18", "@storybook/components": "^6.4.18", "@storybook/core-events": "^6.4.18", "@storybook/preset-scss": "^1.0.3", "@storybook/theming": "^6.4.18", "@storybook/vue": "^6.4.8", "@types/jest": "^28.1.7", "@types/lodash-es": "^4.17.5", "@vue/babel-preset-app": "^4.5.13", "@vue/cli-plugin-babel": "^4.5.13", "@vue/cli-service": "^4.5.13", "@vue/compiler-sfc": "^3.2.30", "@vue/test-utils": "^1.2.2", "@vue/vue2-jest": "^27.0.0-alpha.4", "acorn": "^6.4.1", "add": "^2.0.6", "ajv": "^6.5.2", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^27.0.6", "babel-loader": "^8.2.3", "css-loader": "^3.6.0", "eslint": "*", "eslint-plugin-vue": "^7.11.1", "is-ci-cli": "^2.2.0", "jest": "^28.1.3", "jest-environment-jsdom": "^28.1.3", "jest-junit": "^14.0.0", "jest-transform-stub": "^2.0.0", "jest-watch-select-projects": "^2.0.0", "jest-watch-typeahead": "^0.6.4", "jquery": "^3.5.1", "pug": "^2.0.4", "pug-loader": "^2.4.0", "pug-plain-loader": "^1.0.0", "raw-loader": "^4.0.1", "react": "^17.0.2", "react-dom": "^17.0.2", "sass": "^1.32", "sass-loader": "^10", "storybook-addon-designs": "^6.2.0", "storybook-dark-mode": "^1.0.8", "style-loader": "^2.0.0", "vue-loader": "^15.9.8", "vue-template-compiler": "^2.6.10", "webpack": "^4", "webpack-bundle-analyzer": "^4.4.2"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"]}