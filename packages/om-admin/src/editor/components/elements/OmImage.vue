<template lang="pug">
element-resize(
  v-if="!isSSR"
  :uid="item.uid"
  elementType="OmImage"
  :disableHeightResize="true"
  @element-resize="$emit('element-resize', $event)"
)
  .om-image(:id="`${item.uid}_align`" :style="imageStyle")
    img.om-image(v-if="editMode && !loaded" :id="item.uid" :src="imgSrc")
    a(
      v-else-if="isImageRedirect"
      :class="{ 'om-image-redirect': true, 'disable-link': inEditor }"
      :data-om-image-id="item.uid"
      :href="redirectUrl"
      :target="target"
      :data-om-settings="redirectSettings"
    )
      picture
        source(v-if="mobileUrl" :srcset="mobileUrl" media="(max-width: 576px)")
        source(:srcset="url")
        img.om-image(:id="item.uid" :src="url")
    picture(v-else="")
      source(v-if="mobileUrl" :srcset="mobileUrl" media="(max-width: 576px)")
      source(:srcset="url")
      img.om-image(:id="item.uid" :src="url")
  div PC: {{ dominantColors.desktop }}
  div 
  img(:style="{ display: 'none' }" @load="getDominantColor($event, 'desktop')" :src="url")
  img(
    v-if="url && url !== mobileUrl"
    :style="{ display: 'none' }"
    @load="getDominantColor($event, 'mobile')"
    :src="mobileUrl"
  )
.om-image(:id="`${item.uid}_align`" :style="imageStyle" v-else="")
  img.om-image(v-if="editMode && !loaded" :id="item.uid" :src="imgSrc")
  a(
    v-else-if="isImageRedirect"
    :class="{ 'om-image-redirect': true, 'disable-link': inEditor }"
    :data-om-image-id="item.uid"
    :href="redirectUrl"
    :target="target"
    :data-om-settings="redirectSettings"
  )
    picture
      source(v-if="mobileUrl" :srcset="mobileUrl" media="(max-width: 576px)")
      source(:srcset="url")
      img.om-image(:id="item.uid" :src="url")
  picture(v-else="")
    source(v-if="mobileUrl" :srcset="mobileUrl" media="(max-width: 576px)")
    source(:srcset="url")
    img.om-image(:id="item.uid" :src="url")
</template>
<script>
  import { FastAverageColor } from 'fast-average-color';
  import tinycolor from 'tinycolor2';
  import ElementResize from '@/editor/components/visualizers/ElementResize.vue';
  import lazyImage from '@/mixins/lazyImage';
  import image from '@/mixins/image';

  export default {
    components: { ElementResize },
    mixins: [lazyImage, image],
    props: ['isSSR'],
    data: () => ({ dominantColors: { desktop: null, mobile: null } }),
    methods: {
      async getDominantColor(event, type) {
        try {
          const fac = new FastAverageColor();
          console.log('fac', fac);
          const color = await fac.getColorAsync(event.target);

          console.log('color', color);

          this.dominantColors[type] = tinycolor(color.hex);
        } catch (e) {
          console.log('error', e);
          this.dominantColors[type] = null;
        }
      },
    },
  };
</script>
