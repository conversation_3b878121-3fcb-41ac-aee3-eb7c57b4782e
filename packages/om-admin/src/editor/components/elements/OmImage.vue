<template lang="pug">
element-resize(
  v-if="!isSSR"
  :uid="item.uid"
  elementType="OmImage"
  :disableHeightResize="true"
  @element-resize="$emit('element-resize', $event)"
)
  .om-image(:id="`${item.uid}_align`" :style="mergedStyles")
    img.om-image(v-if="editMode && !loaded" :id="item.uid" :src="imgSrc")
    a(
      v-else-if="isImageRedirect"
      :class="{ 'om-image-redirect': true, 'disable-link': inEditor }"
      :data-om-image-id="item.uid"
      :href="redirectUrl"
      :target="target"
      :data-om-settings="redirectSettings"
    )
      picture
        source(v-if="mobileUrl" :srcset="mobileUrl" media="(max-width: 576px)")
        source(:srcset="url")
        img.om-image(:id="item.uid" :src="url")
    picture(v-else="")
      source(v-if="mobileUrl" :srcset="mobileUrl" media="(max-width: 576px)")
      source(:srcset="url")
      img.om-image(:id="item.uid" :src="url")
  div {{ mergedStyles }} - {{ desktopContrast }} - {{ mobileContrast }}
.om-image(:id="`${item.uid}_align`" :style="imageStyle" v-else="")
  img.om-image(v-if="editMode && !loaded" :id="item.uid" :src="imgSrc")
  a(
    v-else-if="isImageRedirect"
    :class="{ 'om-image-redirect': true, 'disable-link': inEditor }"
    :data-om-image-id="item.uid"
    :href="redirectUrl"
    :target="target"
    :data-om-settings="redirectSettings"
  )
    picture
      source(v-if="mobileUrl" :srcset="mobileUrl" media="(max-width: 576px)")
      source(:srcset="url")
      img.om-image(:id="item.uid" :src="url")
  picture(v-else="")
    source(v-if="mobileUrl" :srcset="mobileUrl" media="(max-width: 576px)")
    source(:srcset="url")
    img.om-image(:id="item.uid" :src="url")
</template>
<script>
  import { FastAverageColor } from 'fast-average-color';
  import tinycolor from 'tinycolor2';
  import ElementResize from '@/editor/components/visualizers/ElementResize.vue';
  import lazyImage from '@/mixins/lazyImage';
  import image from '@/mixins/image';

  export default {
    components: { ElementResize },
    mixins: [lazyImage, image],
    props: ['isSSR'],
    data: () => ({
      desktopContrast: null,
      mobileContrast: null,
    }),
    computed: {
      mergedStyles() {
        return {
          ...this.imageStyle,
          '--mobile-contrast-white': `${this.mobileContrast?.onWhite || 0}`,
          '--mobile-contrast-black': `${this.mobileContrast?.onBlack || 0}`,
          '--desktop-contrast-white': `${this.desktopContrast?.onWhite || 0}`,
          '--desktop-contrast-black': `${this.desktopContrast?.onBlack || 0}`,
        };
      },
    },
    watch: {
      url: {
        handler(url) {
          this.getDominantColorContrasts(url, 'desktop');
        },
        immediate: true,
      },
      mobileUrl: {
        handler(url) {
          this.getDominantColorContrasts(url, 'mobile');
        },
        immediate: true,
      },
    },
    mounted() {
      if (!this.desktopUrl && !this.mobileUrl) return;
      this.getDominantColorContrasts(this.url, 'desktop');
      this.getDominantColorContrasts(this.mobileUrl, 'mobile');
    },
    methods: {
      async getDominantColorContrasts(url, type) {
        if (!url) return;

        try {
          const fac = new FastAverageColor();
          const color = await fac.getColorAsync(url, { algorithm: 'dominant' });
          console.log('color', color);

          this[`${type}Contrast`] = this.calculateContrast(color);
        } catch (e) {
          console.log(`[AVG COLOR][${type}]`, e);
        }
      },

      calculateContrast(color) {
        const avg = tinycolor(color);

        return {
          onWhite: tinycolor.readability(tinycolor('#ffffff')avg, ),
          onBlack: tinycolor.readability(avg, tinycolor('#000000')),
        };
      },
    },
  };
</script>
